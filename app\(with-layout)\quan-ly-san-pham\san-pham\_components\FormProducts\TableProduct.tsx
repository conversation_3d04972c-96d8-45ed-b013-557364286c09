import { useFormContext, useFieldArray } from 'react-hook-form';
import { IProduct, IProductOption } from '@/apis/product/product.type';
import { installationType } from '@/constants/sharedData/sharedData';
import { Button, Col, Table } from 'reactstrap';
import { useState, useEffect } from 'react';

interface TableProductProps {
    initValue?: IProduct;
    page: string;
}

const TableProduct = ({ initValue, page }: TableProductProps) => {
    const { control, watch, setValue } = useFormContext<IProduct>();
    const { fields, append, remove } = useFieldArray({
        control,
        name: 'productOptions',
    });
    useEffect(() => {
        if (
            initValue?.productOptions &&
            initValue.productOptions.length > 0 &&
            fields.length === 0
        ) {
            initValue.productOptions.forEach((option) => {
                append({
                    ...option,
                    yearsOfUse: option.yearsOfUse || 2025,
                    productOptionType: option.productOptionType || 1,
                });
            });
        }
    }, [initValue?.productOptions, append, fields.length]);

    useEffect(() => {
        fields.forEach((_, index) => {
            setValue(`productOptions.${index}.yearsOfUse`, 2025);
            setValue(`productOptions.${index}.productOptionType`, 1);
        });
    }, [fields, setValue]);


    const handleAddRow = () => {
        append({
            installationType: 1,
            userCount: 0,
            basePrice: 0,
            yearsOfUse: 2025,
            productOptionType: 1,
        });
    };

    const handleRemoveRow = (index: number) => {
        remove(index);
    };

    const watchedValues = watch('productOptions');

    const getInstallationTypeLabel = (value: number) => {
        const option = installationType.find(
            (item) => item.value === value.toString(),
        );
        return option ? option.label : '';
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('vi-VN').format(price);
    };

    return (
        <Col xs={12}>
            <div>
                {page !== 'chi-tiet' && (
                    <div
                        style={{
                            marginTop: '16px',
                            display: 'flex',
                            justifyContent: 'flex-end',
                        }}
                    >
                        <Button
                            onClick={handleAddRow}
                            style={{
                                backgroundColor: '#ffffff',
                                borderColor: '#0ab39c',
                                color: '#0ab39c',
                            }}
                        >
                            + Thêm tùy chọn sản phẩm
                        </Button>
                    </div>
                )}

                <Table style={{ marginTop: '20px' }}>
                    <thead
                        style={{
                            backgroundColor: '#f3f6f9',
                        }}
                    >
                        <tr>
                            <th style={{ width: '5%' }}>STT</th>
                            <th style={{ width: '30%' }}>Kiểu cài đặt</th>
                            <th style={{ width: '30%' }}>Số lượng user</th>
                            <th style={{ width: '25%' }}>Giá gốc</th>
                            {page !== 'chi-tiet' && (
                                <th style={{ width: '10%' }}>Thao tác</th>
                            )}
                        </tr>
                    </thead>
                    <tbody>
                        {fields.length === 0 && (
                            <tr>
                                <td
                                    colSpan={5}
                                    style={{
                                        textAlign: 'center',
                                        padding: '20px',
                                        color: '#6c757d',
                                    }}
                                >
                                    Chưa có thông tin
                                </td>
                            </tr>
                        )}

                        {fields.map((field, index) => {
                            const currentValue = watchedValues?.[index];
                            const installationTypeValue =
                                currentValue?.installationType ||
                                field.installationType ||
                                1;
                            const userCountValue =
                                currentValue?.userCount || field.userCount || 0;
                            const basePriceValue =
                                currentValue?.basePrice || field.basePrice || 0;

                            return (
                                <tr key={field.id}>
                                    <td>{index + 1}</td>
                                    <td>
                                        {getInstallationTypeLabel(
                                            installationTypeValue,
                                        )}
                                    </td>
                                    <td>{userCountValue}</td>
                                    <td>{formatPrice(basePriceValue)} USD</td>
                                    {page !== 'chi-tiet' && (
                                        <td>
                                            <button
                                                type='button'
                                                onClick={() =>
                                                    handleRemoveRow(index)
                                                }
                                                title='Xóa'
                                                style={{
                                                    backgroundColor: '#fde8e8',
                                                    color: '#e74c3c',
                                                    border: 'none',
                                                    padding: '4px ',
                                                    width: '25px',
                                                    height: '25px',
                                                    borderRadius: '6px',
                                                    cursor: 'pointer',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                            >
                                                <i className='ri-delete-bin-line'></i>
                                            </button>
                                        </td>
                                    )}
                                </tr>
                            );
                        })}

                        {page !== 'chi-tiet' && isCreating && (
                            <tr>
                                <td>
                                    <div
                                        style={{
                                            padding: '4px 0px 4px 0px',
                                        }}
                                    >
                                        {fields.length + 1}
                                    </div>
                                </td>
                                <td>
                                    <select
                                        value={newRow.installationType}
                                        onChange={(e) =>
                                            setNewRow({
                                                ...newRow,
                                                installationType: parseInt(
                                                    e.target.value,
                                                ),
                                            })
                                        }
                                        style={{
                                            padding: '4px 0px 4px 0px',
                                            width: '30%',
                                        }}
                                    >
                                        {installationType.map((option) => (
                                            <option
                                                key={option.value}
                                                value={option.value}
                                            >
                                                {option.label}
                                            </option>
                                        ))}
                                    </select>
                                </td>
                                <td>
                                    <input
                                        type='number'
                                        value={newRow.userCount || ''}
                                        onChange={(e) =>
                                            setNewRow({
                                                ...newRow,
                                                userCount:
                                                    parseInt(e.target.value) ||
                                                    0,
                                            })
                                        }
                                        placeholder='Nhập số lượng user'
                                        min='0'
                                        style={{
                                            padding: '4px 0px 4px 0px',
                                        }}
                                    />
                                </td>
                                <td>
                                    <input
                                        type='number'
                                        value={newRow.basePrice || ''}
                                        onChange={(e) =>
                                            setNewRow({
                                                ...newRow,
                                                basePrice:
                                                    parseFloat(
                                                        e.target.value,
                                                    ) || 0,
                                            })
                                        }
                                        placeholder='Nhập giá gốc (USD)'
                                        min='0'
                                        step='0.01'
                                        style={{
                                            padding: '10px 0px 4px 0px',
                                        }}
                                    />
                                </td>
                                <td>
                                    <div
                                        style={{
                                            display: 'flex',
                                            gap: '15px',
                                        }}
                                    >
                                        <button
                                            type='button'
                                            onClick={handleConfirmAdd}
                                            title='Xác nhận'
                                            style={{
                                                backgroundColor: '#d0f0ea',
                                                color: '#0ab39c',
                                                border: 'none',
                                                padding: '4px ',
                                                width: '30px',
                                                height: '30px',
                                                borderRadius: '6px',
                                                cursor: 'pointer',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <i className='ri-check-line'></i>
                                        </button>
                                        <button
                                            type='button'
                                            onClick={handleCancelAdd}
                                            title='Hủy'
                                            style={{
                                                backgroundColor: '#fde8e8',
                                                color: '#e74c3c',
                                                border: 'none',
                                                padding: '4px ',
                                                width: '30px',
                                                height: '30px',
                                                borderRadius: '6px',
                                                cursor: 'pointer',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <i className='ri-close-line'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        )}
                    </tbody>
                </Table>
            </div>
        </Col>
    );
};
export default TableProduct;
